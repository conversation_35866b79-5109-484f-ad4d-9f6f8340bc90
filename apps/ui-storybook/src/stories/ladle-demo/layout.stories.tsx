import React from 'react';
import { Box, Stack } from '@bika/ui/layout-components';
import { Typography } from '@bika/ui/text-components';
import { ButtonComponent as But<PERSON> } from '@bika/ui/button';

export default {
  title: 'Ladle Demo/Layout',
};

export const BoxBasic = () => (
  <Box sx={{ p: 2, border: '1px solid #ccc', borderRadius: '8px' }}>
    <Typography level="body-md">
      This is content inside a Box component with padding and border
    </Typography>
  </Box>
);

export const StackVertical = () => (
  <Stack spacing={2}>
    <Button variant="solid" color="primary">First Button</Button>
    <Button variant="outlined" color="neutral">Second Button</Button>
    <Button variant="soft" color="success">Third Button</Button>
  </Stack>
);

export const StackHorizontal = () => (
  <Stack direction="row" spacing={2}>
    <Button variant="solid" color="primary">First</Button>
    <Button variant="outlined" color="neutral">Second</Button>
    <Button variant="soft" color="success">Third</Button>
  </Stack>
);

export const NestedLayout = () => (
  <Box sx={{ p: 3, border: '2px solid #e0e0e0', borderRadius: '12px' }}>
    <Typography level="h3" sx={{ mb: 2 }}>
      Nested Layout Example
    </Typography>
    <Stack spacing={3}>
      <Box sx={{ p: 2, backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
        <Typography level="body-md">
          This is a nested box with background color
        </Typography>
      </Box>
      <Stack direction="row" spacing={2}>
        <Box sx={{ flex: 1, p: 2, border: '1px dashed #ccc' }}>
          <Typography level="body-sm">Left column</Typography>
        </Box>
        <Box sx={{ flex: 1, p: 2, border: '1px dashed #ccc' }}>
          <Typography level="body-sm">Right column</Typography>
        </Box>
      </Stack>
    </Stack>
  </Box>
);
