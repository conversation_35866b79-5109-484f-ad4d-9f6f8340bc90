import React from 'react';
import { Typography } from '@bika/ui/text-components';

export default {
  title: 'Ladle Demo/Typography',
};

export const Heading1 = () => (
  <Typography level="h1">
    This is a Heading 1
  </Typography>
);

export const Heading2 = () => (
  <Typography level="h2">
    This is a Heading 2
  </Typography>
);

export const Heading3 = () => (
  <Typography level="h3">
    This is a Heading 3
  </Typography>
);

export const Body = () => (
  <Typography level="body-md">
    This is body text with medium size
  </Typography>
);

export const BodySmall = () => (
  <Typography level="body-sm">
    This is body text with small size
  </Typography>
);

export const BodyLarge = () => (
  <Typography level="body-lg">
    This is body text with large size
  </Typography>
);

export const ColorVariants = () => (
  <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
    <Typography level="body-md" color="primary">
      Primary color text
    </Typography>
    <Typography level="body-md" color="neutral">
      Neutral color text
    </Typography>
    <Typography level="body-md" color="danger">
      Danger color text
    </Typography>
    <Typography level="body-md" color="success">
      Success color text
    </Typography>
    <Typography level="body-md" color="warning">
      Warning color text
    </Typography>
  </div>
);
